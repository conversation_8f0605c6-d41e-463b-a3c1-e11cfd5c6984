'use client'

import { TremorButton } from "@/components/tremor-button"
import { VisualEditor } from "@/components/visual-editor"

export default function TremorButtonDemo() {
  const initialProps = {
    children: "Click me",
    variant: "primary" as const,
    isLoading: false,
    disabled: false,
  }

  const editableProperties = [
    {
      key: "children",
      label: "Button Text",
      type: "text" as const,
    },
    {
      key: "variant", 
      label: "Variant",
      type: "select" as const,
      options: ["primary", "secondary", "light", "ghost", "destructive"],
    },
    {
      key: "isLoading",
      label: "Loading State", 
      type: "boolean" as const,
    },
    {
      key: "disabled",
      label: "Disabled",
      type: "boolean" as const,
    },
    {
      key: "loadingText",
      label: "Loading Text",
      type: "text" as const,
    },
  ]

  return (
    <div className="container mx-auto py-10">
      <VisualEditor
        componentName="TremorButton"
        component={TremorButton}
        initialProps={initialProps}
        editableProperties={editableProperties}
      />
    </div>
  )
}
