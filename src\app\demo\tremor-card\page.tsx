'use client'

import { TremorCard } from "@/components/tremor-card"
import { VisualEditor } from "@/components/visual-editor"

export default function TremorCardDemo() {
  const initialProps = {
    children: (
      <div>
        <h3 className="text-lg font-semibold mb-2">Card Title</h3>
        <p className="text-muted-foreground">
          This is a sample card content. You can customize the padding, background, and other properties.
        </p>
      </div>
    ),
    className: "",
  }

  const editableProperties = [
    {
      key: "className",
      label: "Custom Classes",
      type: "text" as const,
    },
  ]

  return (
    <div className="container mx-auto py-10">
      <VisualEditor
        componentName="TremorCard"
        component={TremorCard}
        initialProps={initialProps}
        editableProperties={editableProperties}
      />
    </div>
  )
}
