import { VisualEditor } from "@/components/visual-editor";
import { <PERSON><PERSON> } from "@/components/ui/button";

export const visualEditor = {
  name: "visual-editor",
  components: {
    Default: (
      <VisualEditor
        componentName="Button"
        component={Button}
        initialProps={{
          children: "Click me",
          variant: "default",
          size: "default",
          disabled: false,
        }}
        editableProperties={[
          {
            key: "children",
            label: "Button Text",
            type: "text",
          },
          {
            key: "variant",
            label: "Variant",
            type: "select",
            options: ["default", "destructive", "outline", "secondary", "ghost", "link"],
          },
          {
            key: "size",
            label: "Size",
            type: "select",
            options: ["default", "sm", "lg", "icon"],
          },
          {
            key: "disabled",
            label: "Disabled",
            type: "boolean",
          },
        ]}
      />
    ),
  },
};
