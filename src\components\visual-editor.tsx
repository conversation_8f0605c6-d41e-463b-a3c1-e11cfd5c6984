'use client'

import { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface ComponentProps {
  [key: string]: any
}

interface EditableProperty {
  key: string
  label: string
  type: 'text' | 'color' | 'select' | 'number' | 'boolean'
  options?: string[]
}

interface VisualEditorProps {
  componentName: string
  component: React.ComponentType<any>
  initialProps: ComponentProps
  editableProperties: EditableProperty[]
  onPropsChange?: (props: ComponentProps) => void
}

export function VisualEditor({
  componentName,
  component: Component,
  initialProps,
  editableProperties,
  onPropsChange
}: VisualEditorProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [currentProps, setCurrentProps] = useState<ComponentProps>(initialProps)
  const [customCSS, setCustomCSS] = useState('')

  const updateProp = useCallback((key: string, value: any) => {
    const newProps = { ...currentProps, [key]: value }
    setCurrentProps(newProps)
    onPropsChange?.(newProps)
  }, [currentProps, onPropsChange])

  const generateCode = useCallback(() => {
    const propsString = Object.entries(currentProps)
      .map(([key, value]) => {
        if (typeof value === 'string') {
          return `${key}="${value}"`
        }
        if (typeof value === 'boolean') {
          return value ? key : ''
        }
        return `${key}={${JSON.stringify(value)}}`
      })
      .filter(Boolean)
      .join(' ')
    
    return `<${componentName} ${propsString}>${currentProps.children || 'Content'}</${componentName}>`
  }, [componentName, currentProps])

  const PropertyEditor = ({ property }: { property: EditableProperty }) => {
    const value = currentProps[property.key]

    switch (property.type) {
      case 'text':
        return (
          <div className="space-y-2">
            <Label htmlFor={property.key}>{property.label}</Label>
            <Input
              id={property.key}
              value={value || ''}
              onChange={(e) => updateProp(property.key, e.target.value)}
              placeholder={`Enter ${property.label.toLowerCase()}`}
            />
          </div>
        )
      
      case 'color':
        return (
          <div className="space-y-2">
            <Label htmlFor={property.key}>{property.label}</Label>
            <div className="flex gap-2">
              <Input
                id={property.key}
                type="color"
                value={value || '#000000'}
                onChange={(e) => updateProp(property.key, e.target.value)}
                className="w-12 h-8 p-0 border-0"
              />
              <Input
                value={value || ''}
                onChange={(e) => updateProp(property.key, e.target.value)}
                placeholder="#000000"
              />
            </div>
          </div>
        )
      
      case 'select':
        return (
          <div className="space-y-2">
            <Label htmlFor={property.key}>{property.label}</Label>
            <Select 
              value={value || ''} 
              onValueChange={(newValue) => updateProp(property.key, newValue)}
            >
              <SelectTrigger>
                <SelectValue placeholder={`Select ${property.label.toLowerCase()}`} />
              </SelectTrigger>
              <SelectContent>
                {property.options?.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )
      
      case 'number':
        return (
          <div className="space-y-2">
            <Label htmlFor={property.key}>{property.label}</Label>
            <Input
              id={property.key}
              type="number"
              value={value || 0}
              onChange={(e) => updateProp(property.key, parseInt(e.target.value, 10))}
              placeholder="0"
            />
          </div>
        )
      
      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <input
              id={property.key}
              type="checkbox"
              checked={value || false}
              onChange={(e) => updateProp(property.key, e.target.checked)}
              className="w-4 h-4"
            />
            <Label htmlFor={property.key}>{property.label}</Label>
          </div>
        )
      
      default:
        return null
    }
  }

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{componentName}</h2>
          <p className="text-muted-foreground">Customize and preview your component</p>
        </div>
        <Button
          onClick={() => setIsEditing(!isEditing)}
          variant={isEditing ? "secondary" : "default"}
        >
          {isEditing ? 'Preview' : 'Edit'}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Component Preview */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Preview
                {isEditing && <Badge variant="secondary">Edit Mode</Badge>}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div 
                className={`p-8 border-2 border-dashed rounded-lg transition-colors ${
                  isEditing 
                    ? 'border-primary bg-primary/5 hover:bg-primary/10' 
                    : 'border-border bg-muted/30'
                }`}
                style={{ 
                  ...(customCSS && { 
                    background: `linear-gradient(rgba(255,255,255,0.9), rgba(255,255,255,0.9)), ${customCSS}` 
                  }) 
                }}
              >
                <Component {...currentProps} />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Editor Panel */}
        <div className="space-y-4">
          {isEditing && (
            <Card>
              <CardHeader>
                <CardTitle>Properties</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Tabs defaultValue="properties" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="properties">Properties</TabsTrigger>
                    <TabsTrigger value="styling">Styling</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="properties" className="space-y-4">
                    {editableProperties.map((property) => (
                      <PropertyEditor key={property.key} property={property} />
                    ))}
                  </TabsContent>
                  
                  <TabsContent value="styling" className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="custom-css">Custom Background</Label>
                      <Input
                        id="custom-css"
                        value={customCSS}
                        onChange={(e) => setCustomCSS(e.target.value)}
                        placeholder="e.g., #f0f0f0 or url(image.jpg)"
                      />
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}

          {/* Code Output */}
          <Card>
            <CardHeader>
              <CardTitle>Generated Code</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-md">
                <pre className="text-sm overflow-x-auto whitespace-pre-wrap">
                  <code>{generateCode()}</code>
                </pre>
              </div>
              <Button 
                className="mt-2 w-full" 
                variant="outline" 
                onClick={() => navigator.clipboard?.writeText(generateCode())}
              >
                Copy Code
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
