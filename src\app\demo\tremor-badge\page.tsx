'use client'

import { TremorBadge } from "@/components/tremor-badge"
import { VisualEditor } from "@/components/visual-editor"

export default function TremorBadgeDemo() {
  const initialProps = {
    children: "Badge",
    variant: "default" as const,
  }

  const editableProperties = [
    {
      key: "children",
      label: "Badge Text",
      type: "text" as const,
    },
    {
      key: "variant",
      label: "Variant",
      type: "select" as const,
      options: ["default", "neutral", "success", "error", "warning"],
    },
  ]

  return (
    <div className="container mx-auto py-10">
      <VisualEditor
        componentName="TremorBadge"
        component={TremorBadge}
        initialProps={initialProps}
        editableProperties={editableProperties}
      />
    </div>
  )
}
