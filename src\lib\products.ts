import { z } from "zod";

const ProductSchema = z.object({
  id: z.number(),
  name: z.string(),
  price: z.number(),
  description: z.string(),
  category: z.string(),
  brand: z.string(),
  stock: z.number(),
});

export type Product = z.infer<typeof ProductSchema>;

// Mock products data for demo purposes
const mockProducts: Product[] = [
  {
    id: 1,
    name: "Wireless Headphones",
    price: 99.99,
    description: "High-quality wireless headphones with noise cancellation",
    category: "Electronics",
    brand: "TechBrand",
    stock: 50
  },
  {
    id: 2,
    name: "Smart Watch",
    price: 199.99,
    description: "Feature-rich smartwatch with health tracking",
    category: "Electronics",
    brand: "WearTech",
    stock: 30
  },
  {
    id: 3,
    name: "Coffee Mug",
    price: 15.99,
    description: "Ceramic coffee mug with ergonomic handle",
    category: "Home & Kitchen",
    brand: "HomeGoods",
    stock: 100
  },
  {
    id: 4,
    name: "Laptop Stand",
    price: 49.99,
    description: "Adjustable aluminum laptop stand for better ergonomics",
    category: "Office",
    brand: "WorkSpace",
    stock: 25
  },
  {
    id: 5,
    name: "Bluetooth Speaker",
    price: 79.99,
    description: "Portable Bluetooth speaker with excellent sound quality",
    category: "Electronics",
    brand: "SoundWave",
    stock: 40
  },
  {
    id: 6,
    name: "Desk Organizer",
    price: 24.99,
    description: "Bamboo desk organizer with multiple compartments",
    category: "Office",
    brand: "EcoOffice",
    stock: 60
  }
];

export async function getProducts(): Promise<Product[]> {
  try {
    // For demo purposes, we'll use mock data instead of fetching from an external API
    // In a real application, you would fetch from your actual API endpoint

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100));

    return z.array(ProductSchema).parse(mockProducts);
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("Validation error:", error.issues);
    } else {
      console.error("Error fetching products:", error);
    }
    throw error;
  }
}

export async function getCategories(): Promise<string[]> {
  try {
    const products = await getProducts();
    return Array.from(new Set(products.map((product) => product.category)));
  } catch (error) {
    console.error("Error fetching categories:", error);
    throw error;
  }
}
