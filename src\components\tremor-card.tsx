// Tremor Raw Card [v0.0.1]

import { Slot } from "@radix-ui/react-slot"
import React from "react"

import { cx } from "@/lib/utils"

interface TremorCardProps extends React.ComponentPropsWithoutRef<"div"> {
  asChild?: boolean
}

const TremorCard = React.forwardRef<HTMLDivElement, TremorCardProps>(
  ({ className, asChild, ...props }, forwardedRef) => {
    const Component = asChild ? Slot : "div"
    return (
      <Component
        ref={forwardedRef}
        className={cx(
          // base
          "relative w-full rounded-lg border p-6 text-left shadow-sm",
          // background color
          "bg-white dark:bg-[#090E1A]",
          // border color
          "border-gray-200 dark:border-gray-900",
          className,
        )}
        {...props}
      />
    )
  },
)

TremorCard.displayName = "TremorCard"

export { TremorCard, type TremorCardProps }
