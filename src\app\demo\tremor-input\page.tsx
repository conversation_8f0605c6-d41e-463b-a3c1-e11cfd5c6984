'use client'

import { TremorInput } from "@/components/tremor-input"
import { VisualEditor } from "@/components/visual-editor"

export default function TremorInputDemo() {
  const initialProps = {
    placeholder: "Enter text here...",
    type: "text",
    hasError: false,
    disabled: false,
    enableStepper: true,
  }

  const editableProperties = [
    {
      key: "placeholder",
      label: "Placeholder Text",
      type: "text" as const,
    },
    {
      key: "type",
      label: "Input Type",
      type: "select" as const,
      options: ["text", "email", "password", "search", "number", "tel", "url"],
    },
    {
      key: "hasError",
      label: "Has Error",
      type: "boolean" as const,
    },
    {
      key: "disabled",
      label: "Disabled",
      type: "boolean" as const,
    },
    {
      key: "enableStepper",
      label: "Enable Number Stepper",
      type: "boolean" as const,
    },
  ]

  return (
    <div className="container mx-auto py-10">
      <VisualEditor
        componentName="TremorInput"
        component={TremorInput}
        initialProps={initialProps}
        editableProperties={editableProperties}
      />
    </div>
  )
}
